package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.DatasetRequest;
import com.example.biaozhu.payload.response.DatasetResponse;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.service.DatasetService;
import com.example.biaozhu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据集控制器
 * 处理数据集管理相关请求
 */
@RestController
@RequestMapping("/datasets")
public class DatasetController {

    private final DatasetService datasetService;
    private final UserService userService;
    
    @Autowired
    public DatasetController(DatasetService datasetService, UserService userService) {
        this.datasetService = datasetService;
        this.userService = userService;
    }
    
    /**
     * 创建新数据集
     * 
     * @param datasetRequest 数据集请求对象
     * @return 创建的数据集信息
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> createDataset(@Valid @RequestBody DatasetRequest datasetRequest) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Dataset dataset = datasetService.createDataset(datasetRequest, currentUser);
        return new ResponseEntity<>(new DatasetResponse(dataset), HttpStatus.CREATED);
    }
    
    /**
     * 获取所有数据集（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页数据
     */
    @GetMapping
    public ResponseEntity<?> getAllDatasets(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Dataset> datasetsPage = datasetService.getAllDatasets(page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("datasets", datasetsPage.getContent().stream()
                .map(DatasetResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", datasetsPage.getNumber());
        response.put("totalItems", datasetsPage.getTotalElements());
        response.put("totalPages", datasetsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取当前用户的数据集
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页数据
     */
    @GetMapping("/me")
    public ResponseEntity<?> getMyDatasets(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Page<Dataset> datasetsPage = datasetService.getDatasetsByUser(currentUser, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("datasets", datasetsPage.getContent().stream()
                .map(DatasetResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", datasetsPage.getNumber());
        response.put("totalItems", datasetsPage.getTotalElements());
        response.put("totalPages", datasetsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 根据ID获取数据集
     * 
     * @param id 数据集ID
     * @return 数据集信息响应
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getDatasetById(@PathVariable Long id) {
        Dataset dataset = datasetService.getDatasetById(id);
        return ResponseEntity.ok(new DatasetResponse(dataset));
    }
    
    /**
     * 更新数据集信息
     * 
     * @param id 数据集ID
     * @param datasetRequest 数据集请求对象
     * @return 更新后的数据集信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @datasetService.isDatasetCreator(#id)")
    public ResponseEntity<?> updateDataset(
            @PathVariable Long id, 
            @Valid @RequestBody DatasetRequest datasetRequest) {
        
        Dataset updatedDataset = datasetService.updateDataset(id, datasetRequest);
        return ResponseEntity.ok(new DatasetResponse(updatedDataset));
    }
    
    /**
     * 删除数据集
     * 
     * @param id 数据集ID
     * @return 删除成功响应
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @datasetService.isDatasetCreator(#id)")
    public ResponseEntity<?> deleteDataset(@PathVariable Long id) {
        datasetService.deleteDataset(id);
        return ResponseEntity.ok(new MessageResponse("数据集删除成功"));
    }
    
    /**
     * 上传数据集文件
     * 
     * @param id 数据集ID
     * @param files 上传的文件
     * @return 上传结果消息
     */
    @PostMapping("/{id}/upload")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @datasetService.isDatasetCreator(#id)")
    public ResponseEntity<?> uploadDatasetFile(
            @PathVariable Long id,
            @RequestParam("file") MultipartFile[] files) {
        for (MultipartFile file : files) {
            datasetService.uploadDatasetFile(id, file);
        }

        return ResponseEntity.ok(new MessageResponse("数据集文件上传成功"));
    }
    
    /**
     * 导入数据集数据项
     * 
     * @param id 数据集ID
     * @param file 数据项文件（CSV或JSON）
     * @return 导入结果消息
     */
    @PostMapping("/{id}/import")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @datasetService.isDatasetCreator(#id)")
    public ResponseEntity<?> importDataItems(
            @PathVariable Long id,
            @RequestParam("file") MultipartFile file) {
        
        Map<String, Object> result = datasetService.importDataItems(id, file);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 导出数据集数据项及标注
     * 
     * @param id 数据集ID
     * @param format 导出格式（JSON或CSV）
     * @return 导出的文件数据
     */
    @GetMapping("/{id}/export")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('ANNOTATOR') or hasRole('REVIEWER')")
    public ResponseEntity<?> exportDataset(
            @PathVariable Long id,
            @RequestParam(defaultValue = "json") String format) {
        
        ResponseEntity<?> response = datasetService.exportDataset(id, format);
        return response;
    }
    
    /**
     * 根据关键词搜索数据集
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 数据集搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchDatasets(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Page<Dataset> datasetsPage = datasetService.searchDatasets(keyword, page, size);
        
        Map<String, Object> response = new HashMap<>();
        response.put("datasets", datasetsPage.getContent().stream()
                .map(DatasetResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", datasetsPage.getNumber());
        response.put("totalItems", datasetsPage.getTotalElements());
        response.put("totalPages", datasetsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 获取数据集统计信息
     * 
     * @param id 数据集ID
     * @return 数据集统计信息
     */
    @GetMapping("/{id}/statistics")
    public ResponseEntity<?> getDatasetStatistics(@PathVariable Long id) {
        Map<String, Object> statistics = datasetService.getDatasetStatistics(id);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 申请访问数据集
     * 
     * @param id 数据集ID
     * @param reason 申请理由
     * @return 申请结果消息
     */
    @PostMapping("/{id}/access-request")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<?> requestDatasetAccess(
            @PathVariable Long id,
            @RequestParam String reason) {
        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        datasetService.requestDatasetAccess(id, currentUser, reason);
        return ResponseEntity.ok(new MessageResponse("数据集访问申请已提交"));
    }
    
    /**
     * 处理数据集访问请求
     * 
     * @param requestId 请求ID
     * @param approved 是否批准请求
     * @return 处理结果
     */
    @PutMapping("/access-request/{requestId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> processAccessRequest(
            @PathVariable Long requestId,
            @RequestParam boolean approved) {
        
        Map<String, Object> result = datasetService.processAccessRequest(requestId, approved);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 从localStorage迁移数据集到MySQL数据库
     * 
     * @param datasetDTO 数据集DTO
     * @return 迁移结果
     */
    @PostMapping("/migrate-from-local-storage")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> migrateFromLocalStorage(@Valid @RequestBody com.example.biaozhu.dto.DatasetDTO datasetDTO) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        try {
            // 记录请求数据
            System.out.println("接收到迁移请求，数据：" + datasetDTO);
            
            // 验证必要的数据字段
            if (datasetDTO.getName() == null || datasetDTO.getName().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(new MessageResponse("迁移失败: 数据集名称不能为空"));
            }
            
            if (datasetDTO.getProjectId() == null) {
                return ResponseEntity.badRequest().body(new MessageResponse("迁移失败: 项目ID不能为空"));
            }
            
            // 调用服务方法进行迁移
            Dataset dataset = datasetService.migrateDatasetFromLocalStorage(datasetDTO, currentUser);
            return ResponseEntity.ok(new DatasetResponse(dataset));
        } catch (Exception e) {
            // 记录异常详情
            System.err.println("迁移数据集失败: " + e.getMessage());
            e.printStackTrace();
            
            // 返回更详细的错误信息
            String errorMessage = e.getMessage();
            Throwable cause = e.getCause();
            if (cause != null) {
                errorMessage += " 原因: " + cause.getMessage();
            }
            
            return ResponseEntity.badRequest().body(new MessageResponse("迁移失败: " + errorMessage));
        }
    }
    
    /**
     * 获取项目下的所有数据集
     *
     * @param projectId 项目ID
     * @return 数据集列表
     */
    @GetMapping("/project/{projectId}")
    public ResponseEntity<?> getDatasetsByProject(@PathVariable Long projectId) {
        List<Dataset> datasets = datasetService.getDatasetsByProjectId(projectId);
        List<DatasetResponse> responseList = datasets.stream()
                .map(DatasetResponse::new)
                .collect(Collectors.toList());
        return ResponseEntity.ok(responseList);
    }

    /**
     * 获取当前用户可访问的数据集列表（分页）
     * 包括：公开数据集、用户创建的数据集、被授权访问的数据集
     *
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @param type 数据集类型过滤（可选）
     * @param keyword 关键词搜索（可选）
     * @return 用户可访问的数据集分页数据
     */
    @GetMapping("/accessible")
    public ResponseEntity<?> getAccessibleDatasets(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword) {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());

        Page<Dataset> datasetsPage;

        // 根据参数选择不同的查询方法
        if (type != null && !type.trim().isEmpty() && keyword != null && !keyword.trim().isEmpty()) {
            // 同时按类型和关键词过滤
            datasetsPage = datasetService.getAccessibleDatasetsByTypeAndKeyword(currentUser, type.trim(), keyword.trim(), page, size, sort);
        } else if (type != null && !type.trim().isEmpty()) {
            // 按类型过滤
            datasetsPage = datasetService.getAccessibleDatasetsByType(currentUser, type.trim(), page, size, sort);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            // 按关键词搜索
            datasetsPage = datasetService.getAccessibleDatasetsByName(currentUser, keyword.trim(), page, size, sort);
        } else {
            // 获取所有可访问的数据集
            datasetsPage = datasetService.getAccessibleDatasets(currentUser, page, size, sort);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("datasets", datasetsPage.getContent().stream()
                .map(DatasetResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", datasetsPage.getNumber());
        response.put("totalItems", datasetsPage.getTotalElements());
        response.put("totalPages", datasetsPage.getTotalPages());
        response.put("hasNext", datasetsPage.hasNext());
        response.put("hasPrevious", datasetsPage.hasPrevious());

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * 获取当前用户可访问的数据集统计信息
     *
     * @return 数据集访问统计
     */
    @GetMapping("/accessible/stats")
    public ResponseEntity<?> getAccessibleDatasetsStats() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());

        Map<String, Object> stats = new HashMap<>();

        // 总的可访问数据集数量
        long totalAccessible = datasetService.countAccessibleDatasets(currentUser);
        stats.put("totalAccessible", totalAccessible);

        // 按访问类型分类统计
        long publicCount = datasetService.countPublicDatasets();
        long createdByUserCount = datasetService.countDatasetsByCreator(currentUser.getId());
        long authorizedCount = datasetService.countAuthorizedDatasets(currentUser);

        stats.put("publicCount", publicCount);
        stats.put("createdByUserCount", createdByUserCount);
        stats.put("authorizedCount", authorizedCount);

        // 按数据集类型统计
        Map<String, Long> typeStats = datasetService.getAccessibleDatasetsTypeStats(currentUser);
        stats.put("typeStats", typeStats);

        return ResponseEntity.ok(stats);
    }
}