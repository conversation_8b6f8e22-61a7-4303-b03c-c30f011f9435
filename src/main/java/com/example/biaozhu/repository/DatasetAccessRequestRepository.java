package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.DatasetAccessRequest;
import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据集访问请求仓库接口
 */
@Repository
public interface DatasetAccessRequestRepository extends JpaRepository<DatasetAccessRequest, Long> {
    
    /**
     * 根据数据集ID查找所有访问请求
     * 
     * @param datasetId 数据集ID
     * @return 访问请求列表
     */
    List<DatasetAccessRequest> findByDatasetId(Long datasetId);
    
    /**
     * 分页查询特定数据集的访问请求
     * 
     * @param datasetId 数据集ID
     * @param pageable 分页参数
     * @return 访问请求分页结果
     */
    Page<DatasetAccessRequest> findByDatasetId(Long datasetId, Pageable pageable);
    
    /**
     * 查找用户对特定数据集的访问请求
     * 
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @return 访问请求列表
     */
    List<DatasetAccessRequest> findByDatasetIdAndUserId(Long datasetId, Long userId);
    
    /**
     * 根据状态查找访问请求
     * 
     * @param status 请求状态
     * @return 访问请求列表
     */
    List<DatasetAccessRequest> findByStatus(String status);
    
    /**
     * 分页查询特定状态的访问请求
     * 
     * @param status 请求状态
     * @param pageable 分页参数
     * @return 访问请求分页结果
     */
    Page<DatasetAccessRequest> findByStatus(String status, Pageable pageable);
    
    /**
     * 查找用户发起的所有访问请求
     * 
     * @param userId 用户ID
     * @return 访问请求列表
     */
    List<DatasetAccessRequest> findByUserId(Long userId);
    
    /**
     * 分页查询用户发起的访问请求
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 访问请求分页结果
     */
    Page<DatasetAccessRequest> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 查找用户处理的所有访问请求
     * 
     * @param processorId 处理者ID
     * @return 访问请求列表
     */
    List<DatasetAccessRequest> findByProcessorId(Long processorId);
    
    /**
     * 检查用户是否有待处理的数据集访问请求
     * 
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @param status 请求状态
     * @return 是否存在请求
     */
    boolean existsByDatasetIdAndUserIdAndStatus(Long datasetId, Long userId, String status);

    /**
     * 查找特定数据集、用户和状态的访问请求
     *
     * @param dataset 数据集
     * @param user 用户
     * @param status 请求状态
     * @return 访问请求列表
     */
    List<DatasetAccessRequest> findByDatasetAndUserAndStatus(Dataset dataset, User user, String status);

    /**
     * 获取数据集的所有审批记录（成功和正在审批中的）
     *
     * @param datasetId 数据集ID
     * @return 审批记录列表
     */
    @Query("SELECT r FROM DatasetAccessRequest r WHERE r.dataset.id = :datasetId AND r.status IN ('APPROVED', 'PENDING') ORDER BY r.createdAt DESC")
    List<DatasetAccessRequest> findApprovalRecordsByDatasetId(@Param("datasetId") Long datasetId);

    /**
     * 分页获取数据集的所有审批记录（成功和正在审批中的）
     *
     * @param datasetId 数据集ID
     * @param pageable 分页参数
     * @return 审批记录分页结果
     */
    @Query("SELECT r FROM DatasetAccessRequest r WHERE r.dataset.id = :datasetId AND r.status IN ('APPROVED', 'PENDING') ORDER BY r.createdAt DESC")
    Page<DatasetAccessRequest> findApprovalRecordsByDatasetId(@Param("datasetId") Long datasetId, Pageable pageable);

    /**
     * 获取用户对某个数据集的最新审批记录
     *
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @return 最新的审批记录
     */
    @Query("SELECT r FROM DatasetAccessRequest r WHERE r.dataset.id = :datasetId AND r.user.id = :userId ORDER BY r.createdAt DESC")
    List<DatasetAccessRequest> findLatestRequestByDatasetAndUser(@Param("datasetId") Long datasetId, @Param("userId") Long userId);

    /**
     * 获取用户的所有审批记录（按状态过滤）
     *
     * @param userId 用户ID
     * @param statuses 状态列表
     * @param pageable 分页参数
     * @return 审批记录分页结果
     */
    @Query("SELECT r FROM DatasetAccessRequest r WHERE r.user.id = :userId AND r.status IN :statuses ORDER BY r.createdAt DESC")
    Page<DatasetAccessRequest> findUserRequestsByStatuses(@Param("userId") Long userId, @Param("statuses") List<String> statuses, Pageable pageable);
}