package com.example.biaozhu.service;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.DatasetAccessRequest;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.DatasetRequest;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 数据集服务接口
 */
public interface DatasetService {
    
    /**
     * 创建新数据集
     * 
     * @param datasetRequest 数据集请求对象
     * @param creator 创建者
     * @return 创建的数据集
     */
    Dataset createDataset(DatasetRequest datasetRequest, User creator);
    
    /**
     * 获取所有数据集（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getAllDatasets(int page, int size, String sort);
    
    /**
     * 根据ID获取数据集
     * 
     * @param id 数据集ID
     * @return 数据集对象
     */
    Dataset getDatasetById(Long id);
    
    /**
     * 根据名称查找数据集
     * 
     * @param name 数据集名称
     * @return 数据集对象
     */
    Dataset getDatasetByName(String name);
    
    /**
     * 按名称模糊查询数据集（分页）
     * 
     * @param name 数据集名称关键字
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getDatasetsByName(String name, int page, int size, String sort);
    
    /**
     * 按类型查询数据集（分页）
     * 
     * @param type 数据集类型
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getDatasetsByType(String type, int page, int size, String sort);
    
    /**
     * 查询公开数据集（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getPublicDatasets(int page, int size, String sort);
    
    /**
     * 按创建者查询数据集（分页）
     * 
     * @param userId 创建者ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getDatasetsByCreator(Long userId, int page, int size, String sort);
    
    /**
     * 查询用户有权访问的数据集（分页）
     * 
     * @param user 用户
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getAccessibleDatasets(User user, int page, int size, String sort);
    
    /**
     * 按类型查询用户有权访问的数据集（分页）
     * 
     * @param user 用户
     * @param type 数据集类型
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getAccessibleDatasetsByType(User user, String type, int page, int size, String sort);
    
    /**
     * 按名称模糊查询用户有权访问的数据集（分页）
     * 
     * @param user 用户
     * @param name 数据集名称关键字
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getAccessibleDatasetsByName(User user, String name, int page, int size, String sort);
    
    /**
     * 更新数据集
     * 
     * @param id 数据集ID
     * @param datasetRequest 数据集请求对象
     * @return 更新后的数据集
     */
    Dataset updateDataset(Long id, DatasetRequest datasetRequest);
    
    /**
     * 删除数据集
     * 
     * @param id 数据集ID
     */
    void deleteDataset(Long id);
    
    /**
     * 授权用户访问数据集
     * 
     * @param datasetId 数据集ID
     * @param userIds 用户ID列表
     * @return 更新后的数据集
     */
    Dataset authorizeUsers(Long datasetId, List<Long> userIds);
    
    /**
     * 撤销用户访问数据集的权限
     * 
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @return 更新后的数据集
     */
    Dataset revokeUserAccess(Long datasetId, Long userId);
    
    /**
     * 上传文件到数据集
     * 
     * @param datasetId 数据集ID
     * @param files 文件列表
     * @param creator 创建者
     * @return 上传结果统计
     */
    Map<String, Object> uploadFiles(Long datasetId, List<MultipartFile> files, User creator);
    
    /**
     * 导入数据项到数据集
     * 
     * @param datasetId 数据集ID
     * @param jsonContent JSON格式的数据项内容
     * @param creator 创建者
     * @return 导入结果统计
     */
    Map<String, Object> importDataItems(Long datasetId, String jsonContent, User creator);
    
    /**
     * 导出数据集
     * 
     * @param datasetId 数据集ID
     * @param format 导出格式
     * @return 导出的内容
     */
    ResponseEntity<?> exportDataset(Long datasetId, String format);
    
    /**
     * 获取数据集统计信息
     * 
     * @param datasetId 数据集ID
     * @return 统计信息
     */
    Map<String, Object> getDatasetStatistics(Long datasetId);
    
    /**
     * 检查用户是否有权访问数据集
     * 
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @return 是否有权访问
     */
    boolean hasAccessToDataset(Long datasetId, Long userId);
    
    /**
     * 检查用户是否是数据集的创建者
     * 
     * @param datasetId 数据集ID
     * @return 是否是创建者
     */
    boolean isDatasetCreator(Long datasetId);
    
    /**
     * 根据用户获取数据集
     * 
     * @param user 用户
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getDatasetsByUser(User user, int page, int size, String sort);
    
    /**
     * 上传数据集文件
     * 
     * @param datasetId 数据集ID
     * @param file 文件
     * @return 上传结果信息
     */
    Map<String, Object> uploadDatasetFile(Long datasetId, MultipartFile file);
    
    /**
     * 导入数据项到数据集
     * 
     * @param datasetId 数据集ID
     * @param file 数据项文件
     * @return 导入结果统计
     */
    Map<String, Object> importDataItems(Long datasetId, MultipartFile file);
    
    /**
     * 搜索数据集
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 数据集分页结果
     */
    Page<Dataset> searchDatasets(String keyword, int page, int size);
    
    /**
     * 请求访问数据集
     * 
     * @param datasetId 数据集ID
     * @param user 请求用户
     * @param reason 请求原因
     * @return 请求结果信息
     */
    Map<String, Object> requestDatasetAccess(Long datasetId, User user, String reason);
    
    /**
     * 处理数据集访问请求
     * 
     * @param requestId 请求ID
     * @param approved 是否批准
     * @return 处理结果信息
     */
    Map<String, Object> processAccessRequest(Long requestId, boolean approved);

    /**
     * 根据项目ID获取数据集列表
     * 
     * @param projectId 项目ID
     * @return 数据集列表
     */
    List<Dataset> getDatasetsByProjectId(Long projectId);
    
    /**
     * 根据项目ID获取数据集分页列表
     * 
     * @param projectId 项目ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getDatasetsByProjectId(Long projectId, int page, int size, String sort);
    
    /**
     * 从localStorage迁移数据集
     *
     * @param datasetDTO 数据集DTO
     * @param creator 创建者
     * @return 创建的数据集
     */
    Dataset migrateDatasetFromLocalStorage(com.example.biaozhu.dto.DatasetDTO datasetDTO, User creator);

    /**
     * 按类型和关键词查询用户可访问的数据集
     *
     * @param user 用户
     * @param type 数据集类型
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getAccessibleDatasetsByTypeAndKeyword(User user, String type, String keyword, int page, int size, String sort);

    /**
     * 统计用户可访问的数据集数量
     *
     * @param user 用户
     * @return 数据集数量
     */
    long countAccessibleDatasets(User user);

    /**
     * 统计公开数据集数量
     *
     * @return 公开数据集数量
     */
    long countPublicDatasets();

    /**
     * 统计用户创建的数据集数量
     *
     * @param userId 用户ID
     * @return 数据集数量
     */
    long countDatasetsByCreator(Long userId);

    /**
     * 统计用户被授权访问的数据集数量
     *
     * @param user 用户
     * @return 数据集数量
     */
    long countAuthorizedDatasets(User user);

    /**
     * 获取用户可访问数据集的类型统计
     *
     * @param user 用户
     * @return 类型统计Map
     */
    Map<String, Long> getAccessibleDatasetsTypeStats(User user);

    /**
     * 从dataset_access表获取用户已授权的数据集ID列表
     *
     * @param userId 用户ID
     * @return 数据集ID列表
     */
    List<Long> getAuthorizedDatasetIds(Long userId);

    /**
     * 获取用户通过dataset_access表授权的数据集列表（分页）
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 数据集分页结果
     */
    Page<Dataset> getAuthorizedDatasets(Long userId, int page, int size, String sort);

    /**
     * 获取数据集的审批记录（成功和正在审批中的）
     *
     * @param datasetId 数据集ID
     * @return 审批记录列表
     */
    List<DatasetAccessRequest> getDatasetApprovalRecords(Long datasetId);

    /**
     * 分页获取数据集的审批记录（成功和正在审批中的）
     *
     * @param datasetId 数据集ID
     * @param page 页码
     * @param size 每页大小
     * @return 审批记录分页结果
     */
    Page<DatasetAccessRequest> getDatasetApprovalRecords(Long datasetId, int page, int size);

    /**
     * 获取用户对某个数据集的最新审批状态
     *
     * @param datasetId 数据集ID
     * @param userId 用户ID
     * @return 最新的审批记录
     */
    DatasetAccessRequest getLatestUserRequestForDataset(Long datasetId, Long userId);

    /**
     * 获取用户的审批记录（按状态过滤）
     *
     * @param userId 用户ID
     * @param statuses 状态列表
     * @param page 页码
     * @param size 每页大小
     * @return 审批记录分页结果
     */
    Page<DatasetAccessRequest> getUserRequestsByStatuses(Long userId, List<String> statuses, int page, int size);

    /**
     * 获取所有数据集的访问审批记录（管理员用）
     *
     * @param page 页码
     * @param size 每页大小
     * @return 审批记录分页结果
     */
    Page<DatasetAccessRequest> getAllAccessRequests(int page, int size);

    /**
     * 按条件获取所有数据集的访问审批记录（管理员用）
     *
     * @param statuses 状态列表（可为null）
     * @param keyword 搜索关键词（可为null）
     * @param page 页码
     * @param size 每页大小
     * @return 审批记录分页结果
     */
    Page<DatasetAccessRequest> getAllAccessRequestsByConditions(List<String> statuses, String keyword, int page, int size);

    /**
     * 获取审批记录状态统计（管理员用）
     *
     * @return 状态统计Map
     */
    Map<String, Long> getAccessRequestStatusStats();

    /**
     * 获取待处理的审批记录数量
     *
     * @return 待处理记录数量
     */
    long getPendingRequestsCount();
}