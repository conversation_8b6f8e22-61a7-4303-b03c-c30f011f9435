package com.example.biaozhu.service.impl;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.DataItem;
import com.example.biaozhu.entity.DatasetAccessRequest;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.exception.UnauthorizedException;
import com.example.biaozhu.payload.request.DatasetRequest;
import com.example.biaozhu.repository.DataItemRepository;
import com.example.biaozhu.repository.DatasetAccessRequestRepository;
import com.example.biaozhu.repository.DatasetRepository;
import com.example.biaozhu.repository.UserRepository;
import com.example.biaozhu.repository.ProjectRepository;
import com.example.biaozhu.service.DatasetService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据集服务实现类
 */
@Service
public class DatasetServiceImpl implements DatasetService {

    @Value("${file.upload-dir:./uploads}")
    private String uploadDir;

    private final DatasetRepository datasetRepository;
    private final DataItemRepository dataItemRepository;
    private final UserRepository userRepository;
    private final ObjectMapper objectMapper;
    private final DatasetAccessRequestRepository datasetAccessRequestRepository;
    private final ProjectRepository projectRepository;

    @Autowired
    public DatasetServiceImpl(
            DatasetRepository datasetRepository,
            DataItemRepository dataItemRepository,
            UserRepository userRepository,
            ObjectMapper objectMapper,
            DatasetAccessRequestRepository datasetAccessRequestRepository,
            ProjectRepository projectRepository) {
        this.datasetRepository = datasetRepository;
        this.dataItemRepository = dataItemRepository;
        this.userRepository = userRepository;
        this.objectMapper = objectMapper;
        this.datasetAccessRequestRepository = datasetAccessRequestRepository;
        this.projectRepository = projectRepository;
    }

    @Override
    @Transactional
    public Dataset createDataset(DatasetRequest datasetRequest, User creator) {
        // 检查数据集名称是否已存在
        if (datasetRepository.findByName(datasetRequest.getName()).isPresent()) {
            throw new IllegalArgumentException("数据集名称已存在: " + datasetRequest.getName());
        }

        // 创建新数据集
        Dataset dataset = new Dataset();
        dataset.setName(datasetRequest.getName());
        dataset.setDescription(datasetRequest.getDescription());
        dataset.setType(datasetRequest.getType());
        dataset.setPublic(datasetRequest.isPublic());
        dataset.setCreator(creator);
        dataset.setCreatedAt(LocalDateTime.now());
        dataset.setUpdatedAt(LocalDateTime.now());
        
        // 保存数据集
        Dataset savedDataset = datasetRepository.save(dataset);
        
        // 如果提供了授权用户列表，添加用户权限
        if (datasetRequest.getAuthorizedUserIds() != null && !datasetRequest.getAuthorizedUserIds().isEmpty()) {
            for (Long userId : datasetRequest.getAuthorizedUserIds()) {
                User user = userRepository.findById(userId)
                        .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));
                savedDataset.addAuthorizedUser(user);
            }
            savedDataset = datasetRepository.save(savedDataset);
        }
        
        return savedDataset;
    }

    @Override
    public Page<Dataset> getAllDatasets(int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findAll(pageable);
    }

    @Override
    public Dataset getDatasetById(Long id) {
        return datasetRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("数据集未找到，ID: " + id));
    }

    @Override
    public Dataset getDatasetByName(String name) {
        return datasetRepository.findByName(name)
                .orElseThrow(() -> new ResourceNotFoundException("数据集未找到，名称: " + name));
    }

    @Override
    public Page<Dataset> getDatasetsByName(String name, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findByNameContaining(name, pageable);
    }

    @Override
    public Page<Dataset> getDatasetsByType(String type, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findByType(type, pageable);
    }

    @Override
    public Page<Dataset> getPublicDatasets(int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findByIsPublicTrue(pageable);
    }

    @Override
    public Page<Dataset> getDatasetsByCreator(Long userId, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findByCreatorId(userId, pageable);
    }

    @Override
    public Page<Dataset> getAccessibleDatasets(User user, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findAccessibleDatasets(user, pageable);
    }

    @Override
    public Page<Dataset> getAccessibleDatasetsByType(User user, String type, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findAccessibleDatasetsByType(user, type, pageable);
    }

    @Override
    public Page<Dataset> getAccessibleDatasetsByName(User user, String name, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findAccessibleDatasetsByName(user, name, pageable);
    }

    @Override
    @Transactional
    public Dataset updateDataset(Long id, DatasetRequest datasetRequest) {
        Dataset dataset = getDatasetById(id);
        
        // 检查权限
        if (!isDatasetCreator(id) && !hasAdminRole()) {
            throw new UnauthorizedException("无权更新数据集");
        }
        
        // 如果更改了名称，检查名称是否已存在
        if (!dataset.getName().equals(datasetRequest.getName()) &&
                datasetRepository.findByName(datasetRequest.getName()).isPresent()) {
            throw new IllegalArgumentException("数据集名称已存在: " + datasetRequest.getName());
        }
        
        // 更新数据集信息
        dataset.setName(datasetRequest.getName());
        dataset.setDescription(datasetRequest.getDescription());
        dataset.setType(datasetRequest.getType());
        dataset.setPublic(datasetRequest.isPublic());
        dataset.setUpdatedAt(LocalDateTime.now());
        
        return datasetRepository.save(dataset);
    }

    @Override
    @Transactional
    public void deleteDataset(Long id) {
        Dataset dataset = getDatasetById(id);
        
        // 检查权限
        if (!isDatasetCreator(id) && !hasAdminRole()) {
            throw new UnauthorizedException("无权删除数据集");
        }
        
        datasetRepository.delete(dataset);
    }

    @Override
    @Transactional
    public Dataset authorizeUsers(Long datasetId, List<Long> userIds) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 检查权限
        if (!isDatasetCreator(datasetId) && !hasAdminRole()) {
            throw new UnauthorizedException("无权授权数据集访问");
        }
        
        for (Long userId : userIds) {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new ResourceNotFoundException("用户未找到，ID: " + userId));
            dataset.addAuthorizedUser(user);
        }
        
        return datasetRepository.save(dataset);
    }

    @Override
    @Transactional
    public Dataset revokeUserAccess(Long datasetId, Long userId) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 检查权限
        if (!isDatasetCreator(datasetId) && !hasAdminRole()) {
            throw new UnauthorizedException("无权撤销数据集访问");
        }
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("用户未找到，ID: " + userId));
        
        dataset.removeAuthorizedUser(user);
        
        return datasetRepository.save(dataset);
    }

    @Override
    @Transactional
    public Map<String, Object> uploadFiles(Long datasetId, List<MultipartFile> files, User creator) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 检查权限
        if (!hasAccessToDataset(datasetId, creator.getId()) && !hasAdminRole()) {
            throw new UnauthorizedException("无权上传文件到数据集");
        }
        
        Map<String, Object> result = new HashMap<>();
        List<String> uploadedFiles = new ArrayList<>();
        List<String> failedFiles = new ArrayList<>();
        
        // 确保上传目录存在
        Path uploadPath = Paths.get(uploadDir, "dataset-" + datasetId);
        try {
            Files.createDirectories(uploadPath);
        } catch (IOException e) {
            throw new RuntimeException("无法创建上传目录", e);
        }
        
        for (MultipartFile file : files) {
            try {
                // 生成唯一文件名
                String fileName = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();
                Path filePath = uploadPath.resolve(fileName);
                
                // 保存文件
                Files.copy(file.getInputStream(), filePath);
                
                // 创建数据项
                DataItem dataItem = new DataItem();
                dataItem.setName(file.getOriginalFilename());
                dataItem.setType(getDataItemTypeFromFile(file));
                dataItem.setFilePath(filePath.toString());
                dataItem.setFileSize(file.getSize());
                dataItem.setFileType(file.getContentType());
                dataItem.setDataset(dataset);
                dataItem.setCreator(creator);
                dataItem.setCreatedAt(LocalDateTime.now());
                dataItem.setUpdatedAt(LocalDateTime.now());
                
                dataItemRepository.save(dataItem);
                uploadedFiles.add(file.getOriginalFilename());
            } catch (Exception e) {
                failedFiles.add(file.getOriginalFilename() + ": " + e.getMessage());
            }
        }
        
        // 更新数据集信息
        dataset.setItemCount((int) dataItemRepository.countByDatasetId(datasetId));
        dataset.setUpdatedAt(LocalDateTime.now());
        datasetRepository.save(dataset);
        
        result.put("uploadedFiles", uploadedFiles);
        result.put("failedFiles", failedFiles);
        result.put("totalUploaded", uploadedFiles.size());
        result.put("totalFailed", failedFiles.size());
        
        return result;
    }

    private String getDataItemTypeFromFile(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType == null) {
            return "UNKNOWN";
        }
        
        if (contentType.startsWith("text")) {
            return "TEXT";
        } else if (contentType.startsWith("image")) {
            return "IMAGE";
        } else if (contentType.startsWith("audio")) {
            return "AUDIO";
        } else if (contentType.startsWith("video")) {
            return "VIDEO";
        } else {
            return "OTHER";
        }
    }

    @Override
    @Transactional
    public Map<String, Object> importDataItems(Long datasetId, String jsonContent, User creator) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 检查权限
        if (!hasAccessToDataset(datasetId, creator.getId()) && !hasAdminRole()) {
            throw new UnauthorizedException("无权导入数据到数据集");
        }
        
        Map<String, Object> result = new HashMap<>();
        List<String> importedItems = new ArrayList<>();
        List<String> failedItems = new ArrayList<>();
        
        try {
            // 解析JSON内容
            List<Map<String, Object>> items = objectMapper.readValue(jsonContent, 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class));
            
            for (Map<String, Object> item : items) {
                try {
                    String name = (String) item.getOrDefault("name", "Unnamed Item");
                    String type = (String) item.getOrDefault("type", "TEXT");
                    String content = objectMapper.writeValueAsString(item.get("content"));
                    
                    // 创建数据项
                    DataItem dataItem = new DataItem();
                    dataItem.setName(name);
                    dataItem.setType(type);
                    dataItem.setContent(content);
                    dataItem.setDataset(dataset);
                    dataItem.setCreator(creator);
                    dataItem.setCreatedAt(LocalDateTime.now());
                    dataItem.setUpdatedAt(LocalDateTime.now());
                    
                    // 如果有元数据，添加
                    if (item.containsKey("metadata")) {
                        dataItem.setMetadata(objectMapper.writeValueAsString(item.get("metadata")));
                    }
                    
                    dataItemRepository.save(dataItem);
                    importedItems.add(name);
                } catch (Exception e) {
                    failedItems.add(String.valueOf(item.get("name")) + ": " + e.getMessage());
                }
            }
            
            // 更新数据集信息
            dataset.setItemCount((int) dataItemRepository.countByDatasetId(datasetId));
            dataset.setUpdatedAt(LocalDateTime.now());
            datasetRepository.save(dataset);
            
            result.put("importedItems", importedItems);
            result.put("failedItems", failedItems);
            result.put("totalImported", importedItems.size());
            result.put("totalFailed", failedItems.size());
            
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON解析错误: " + e.getMessage(), e);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> requestDatasetAccess(Long datasetId, User user, String reason) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 检查是否已有等待处理的请求
        List<DatasetAccessRequest> pendingRequests = datasetAccessRequestRepository.findByDatasetAndUserAndStatus(
                dataset, user, "PENDING");
        if (!pendingRequests.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "您已有等待处理的访问请求");
            return result;
        }
        
        // 创建新请求
        DatasetAccessRequest request = new DatasetAccessRequest();
        request.setDataset(dataset);
        request.setUser(user);
        request.setReason(reason);
        request.setStatus("PENDING");
        request.setRequestDate(new Date());
        
        // 保存请求
        datasetAccessRequestRepository.save(request);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "访问请求已提交，等待处理");
        return result;
    }

    @Override
    public Map<String, Object> processAccessRequest(Long requestId, boolean approved) {
        DatasetAccessRequest request = datasetAccessRequestRepository.findById(requestId)
                .orElseThrow(() -> new ResourceNotFoundException("访问请求未找到，ID: " + requestId));
        
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userRepository.findByUsername(authentication.getName())
                .orElseThrow(() -> new ResourceNotFoundException("用户未找到"));
        
        // 检查是否是数据集创建者或管理员
        if (!request.getDataset().getCreator().equals(currentUser) && !hasAdminRole()) {
            throw new UnauthorizedException("无权处理访问请求");
        }
        
        // 更新请求状态
        request.setStatus(approved ? "APPROVED" : "REJECTED");
        request.setProcessDate(new Date());
        request.setProcessor(currentUser);
        
        // 如果批准请求，添加用户到授权列表
        if (approved) {
            Dataset dataset = request.getDataset();
            User user = request.getUser();
            dataset.addAuthorizedUser(user);
            datasetRepository.save(dataset);
        }
        
        // 保存请求
        datasetAccessRequestRepository.save(request);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "访问请求已" + (approved ? "批准" : "拒绝"));
        return result;
    }

    @Override
    public ResponseEntity<?> exportDataset(Long datasetId, String format) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 检查权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("未授权访问");
        }
        
        String username = authentication.getName();
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户未找到: " + username));
        
        if (!hasAccessToDataset(datasetId, user.getId()) && !hasAdminRole()) {
            throw new UnauthorizedException("无权导出数据集");
        }
        
        try {
            // 获取数据集的所有数据项
            List<DataItem> dataItems = dataItemRepository.findByDatasetId(datasetId, Pageable.unpaged())
                    .getContent();
            
            byte[] data;
            String contentType;
            String filename = dataset.getName().replaceAll("\\s+", "_") + "_" + datasetId;
            
            if ("json".equalsIgnoreCase(format)) {
                data = exportToJson(dataset, dataItems);
                contentType = "application/json";
                filename += ".json";
            } else if ("csv".equalsIgnoreCase(format)) {
                data = exportToCsv(dataset, dataItems);
                contentType = "text/csv";
                filename += ".csv";
            } else {
                throw new IllegalArgumentException("不支持的导出格式: " + format);
            }
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(data);
        } catch (Exception e) {
            throw new RuntimeException("导出数据集时出错: " + e.getMessage(), e);
        }
    }

    private byte[] exportToJson(Dataset dataset, List<DataItem> dataItems) throws Exception {
        Map<String, Object> root = new HashMap<>();
        Map<String, Object> datasetInfo = new HashMap<>();
        datasetInfo.put("id", dataset.getId());
        datasetInfo.put("name", dataset.getName());
        datasetInfo.put("description", dataset.getDescription());
        datasetInfo.put("type", dataset.getType());
        datasetInfo.put("itemCount", dataset.getItemCount());
        datasetInfo.put("createdAt", dataset.getCreatedAt().toString());
        root.put("dataset", datasetInfo);
        
        List<Map<String, Object>> items = new ArrayList<>();
        for (DataItem item : dataItems) {
            Map<String, Object> itemMap = new HashMap<>();
            itemMap.put("id", item.getId());
            itemMap.put("name", item.getName());
            itemMap.put("type", item.getType());
            itemMap.put("content", item.getContent());
            itemMap.put("filePath", item.getFilePath());
            itemMap.put("fileSize", item.getFileSize());
            itemMap.put("fileType", item.getFileType());
            itemMap.put("metadata", item.getMetadata());
            itemMap.put("createdAt", item.getCreatedAt().toString());
            
            items.add(itemMap);
        }
        root.put("items", items);
        
        return objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsBytes(root);
    }

    private byte[] exportToCsv(Dataset dataset, List<DataItem> dataItems) {
        StringBuilder csv = new StringBuilder();
        csv.append("id,name,type,content,filePath,fileSize,fileType,createdAt\n");
        
        for (DataItem item : dataItems) {
            csv.append(item.getId()).append(",");
            csv.append(escapeCsv(item.getName())).append(",");
            csv.append(item.getType()).append(",");
            csv.append(escapeCsv(item.getContent())).append(",");
            csv.append(escapeCsv(item.getFilePath())).append(",");
            csv.append(item.getFileSize()).append(",");
            csv.append(item.getFileType()).append(",");
            csv.append(item.getCreatedAt()).append("\n");
        }
        
        return csv.toString().getBytes();
    }

    private String escapeCsv(String input) {
        if (input == null) {
            return "";
        }
        return "\"" + input.replace("\"", "\"\"") + "\"";
    }

    @Override
    public Map<String, Object> getDatasetStatistics(Long datasetId) {
        Dataset dataset = getDatasetById(datasetId);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 基本信息
        statistics.put("id", dataset.getId());
        statistics.put("name", dataset.getName());
        statistics.put("type", dataset.getType());
        statistics.put("itemCount", dataset.getItemCount());
        
        // 按类型统计
        List<Object[]> typeStats = dataItemRepository.countByDatasetIdGroupByType(datasetId);
        Map<String, Long> typeCountMap = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeCountMap.put((String) stat[0], (Long) stat[1]);
        }
        statistics.put("typeStats", typeCountMap);
        
        // 标注统计
        long annotatedCount = dataItemRepository.countAnnotatedItemsByDatasetId(datasetId);
        long unannotatedCount = dataItemRepository.countUnannotatedItemsByDatasetId(datasetId);
        
        statistics.put("annotatedCount", annotatedCount);
        statistics.put("unannotatedCount", unannotatedCount);
        statistics.put("annotationRate", dataset.getItemCount() > 0 ? 
                (double) annotatedCount / dataset.getItemCount() : 0);
        
        return statistics;
    }

    @Override
    public boolean hasAccessToDataset(Long datasetId, Long userId) {
        return datasetRepository.hasAccessToDataset(datasetId, userId);
    }

    @Override
    public boolean isDatasetCreator(Long datasetId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        String username = authentication.getName();
        Dataset dataset = datasetRepository.findById(datasetId).orElse(null);
        
        return dataset != null && dataset.getCreator().getUsername().equals(username);
    }
    
    /**
     * 检查当前用户是否具有ADMIN角色
     * 
     * @return 是否是管理员
     */
    private boolean hasAdminRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
    }

    @Override
    public Page<Dataset> searchDatasets(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        
        // 按名称搜索
        return datasetRepository.findByNameContaining(keyword, pageable);
    }

    @Override
    @Transactional
    public Map<String, Object> importDataItems(Long datasetId, MultipartFile file) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("未授权访问");
        }
        
        String username = authentication.getName();
        User creator = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户未找到: " + username));
        
        // 检查权限
        if (!hasAccessToDataset(datasetId, creator.getId()) && !hasAdminRole()) {
            throw new UnauthorizedException("无权导入数据到数据集");
        }
        
        Map<String, Object> result = new HashMap<>();
        List<String> importedItems = new ArrayList<>();
        List<String> failedItems = new ArrayList<>();
        
        try {
            String contentType = file.getContentType();
            String fileName = file.getOriginalFilename();
            
            if (fileName != null && fileName.toLowerCase().endsWith(".json")) {
                // 处理JSON文件
                String jsonContent = new String(file.getBytes());
                return importDataItems(datasetId, jsonContent, creator);
                
            } else if (fileName != null && (fileName.toLowerCase().endsWith(".csv") || 
                    (contentType != null && contentType.equals("text/csv")))) {
                // 处理CSV文件
                return importCsvItems(datasetId, file, creator);
                
            } else if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
                // 处理Excel文件
                return importExcelItems(datasetId, file, creator);
                
            } else {
                // 处理单个文件作为数据项
                return uploadDatasetFile(datasetId, file);
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }
    
    /**
     * 从CSV文件导入数据项
     * 
     * @param datasetId 数据集ID
     * @param file CSV文件
     * @param creator 创建者
     * @return 导入结果
     */
    private Map<String, Object> importCsvItems(Long datasetId, MultipartFile file, User creator) {
        Dataset dataset = getDatasetById(datasetId);
        Map<String, Object> result = new HashMap<>();
        List<String> importedItems = new ArrayList<>();
        List<String> failedItems = new ArrayList<>();
        
        try {
            // 读取CSV内容
            String content = new String(file.getBytes());
            String[] lines = content.split("\n");
            
            // 解析CSV头
            String[] headers = null;
            if (lines.length > 0) {
                headers = lines[0].split(",");
            }
            
            // 处理每一行数据
            for (int i = 1; i < lines.length; i++) {
                try {
                    String[] values = lines[i].split(",");
                    
                    // 创建数据项
                    DataItem dataItem = new DataItem();
                    
                    // 设置基本属性
                    dataItem.setName("Item_" + i);
                    dataItem.setType("TEXT"); // 默认类型
                    dataItem.setContent(lines[i]);
                    dataItem.setDataset(dataset);
                    dataItem.setCreator(creator);
                    dataItem.setCreatedAt(LocalDateTime.now());
                    dataItem.setUpdatedAt(LocalDateTime.now());
                    
                    // 如果CSV的列数和头数一致，可以构建结构化内容
                    if (headers != null && values.length == headers.length) {
                        Map<String, Object> structuredContent = new HashMap<>();
                        for (int j = 0; j < headers.length; j++) {
                            structuredContent.put(headers[j].trim(), values[j].trim());
                        }
                        dataItem.setContent(objectMapper.writeValueAsString(structuredContent));
                        
                        // 如果有name列，使用它作为数据项名称
                        if (structuredContent.containsKey("name")) {
                            dataItem.setName((String) structuredContent.get("name"));
                        }
                        
                        // 如果有type列，使用它作为数据项类型
                        if (structuredContent.containsKey("type")) {
                            dataItem.setType((String) structuredContent.get("type"));
                        }
                    }
                    
                    dataItemRepository.save(dataItem);
                    importedItems.add(dataItem.getName());
                } catch (Exception e) {
                    failedItems.add("行 " + i + ": " + e.getMessage());
                }
            }
            
            // 更新数据集信息
            dataset.setItemCount((int) dataItemRepository.countByDatasetId(datasetId));
            dataset.setUpdatedAt(LocalDateTime.now());
            datasetRepository.save(dataset);
            
            result.put("success", true);
            result.put("importedItems", importedItems);
            result.put("failedItems", failedItems);
            result.put("totalImported", importedItems.size());
            result.put("totalFailed", failedItems.size());
            
            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }
    
    /**
     * 从Excel文件导入数据项
     * 
     * @param datasetId 数据集ID
     * @param file Excel文件
     * @param creator 创建者
     * @return 导入结果
     */
    private Map<String, Object> importExcelItems(Long datasetId, MultipartFile file, User creator) {
        Dataset dataset = getDatasetById(datasetId);
        Map<String, Object> result = new HashMap<>();
        List<String> importedItems = new ArrayList<>();
        List<String> failedItems = new ArrayList<>();
        
        try {
            // 创建工作簿对象
            Workbook workbook = null;
            if (file.getOriginalFilename().toLowerCase().endsWith("xlsx")) {
                workbook = new XSSFWorkbook(file.getInputStream());
            } else if (file.getOriginalFilename().toLowerCase().endsWith("xls")) {
                workbook = new HSSFWorkbook(file.getInputStream());
            } else {
                throw new IllegalArgumentException("不支持的Excel文件格式");
            }
            
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            
            // 获取表头行
            Row headerRow = sheet.getRow(0);
            int columnCount = headerRow.getLastCellNum();
            String[] headers = new String[columnCount];
            
            // 解析表头
            for (int i = 0; i < columnCount; i++) {
                Cell cell = headerRow.getCell(i);
                headers[i] = cell != null ? cell.toString().trim() : "";
            }
            
            // 处理数据行
            int rowCount = sheet.getLastRowNum();
            for (int i = 1; i <= rowCount; i++) {
                try {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;
                    
                    // 创建数据项
                    DataItem dataItem = new DataItem();
                    
                    // 设置基本属性
                    dataItem.setName("Item_" + i);
                    dataItem.setType("TEXT"); // 默认类型
                    dataItem.setDataset(dataset);
                    dataItem.setCreator(creator);
                    dataItem.setCreatedAt(LocalDateTime.now());
                    dataItem.setUpdatedAt(LocalDateTime.now());
                    
                    // 构建结构化内容
                    Map<String, Object> structuredContent = new HashMap<>();
                    for (int j = 0; j < columnCount; j++) {
                        Cell cell = row.getCell(j);
                        String value = "";
                        
                        if (cell != null) {
                            switch (cell.getCellType()) {
                                case STRING:
                                    value = cell.getStringCellValue();
                                    break;
                                case NUMERIC:
                                    if (DateUtil.isCellDateFormatted(cell)) {
                                        value = cell.getDateCellValue().toString();
                                    } else {
                                        value = String.valueOf(cell.getNumericCellValue());
                                    }
                                    break;
                                case BOOLEAN:
                                    value = String.valueOf(cell.getBooleanCellValue());
                                    break;
                                case FORMULA:
                                    value = cell.getCellFormula();
                                    break;
                                default:
                                    value = "";
                            }
                        }
                        
                        structuredContent.put(headers[j], value);
                    }
                    
                    // 设置内容
                    dataItem.setContent(objectMapper.writeValueAsString(structuredContent));
                    
                    // 如果有name列，使用它作为数据项名称
                    if (structuredContent.containsKey("name")) {
                        dataItem.setName((String) structuredContent.get("name"));
                    }
                    
                    // 如果有type列，使用它作为数据项类型
                    if (structuredContent.containsKey("type")) {
                        dataItem.setType((String) structuredContent.get("type"));
                    }
                    
                    dataItemRepository.save(dataItem);
                    importedItems.add(dataItem.getName());
                } catch (Exception e) {
                    failedItems.add("行 " + i + ": " + e.getMessage());
                }
            }
            
            // 关闭工作簿
            workbook.close();
            
            // 更新数据集信息
            dataset.setItemCount((int) dataItemRepository.countByDatasetId(datasetId));
            dataset.setUpdatedAt(LocalDateTime.now());
            datasetRepository.save(dataset);
            
            result.put("success", true);
            result.put("importedItems", importedItems);
            result.put("failedItems", failedItems);
            result.put("totalImported", importedItems.size());
            result.put("totalFailed", failedItems.size());
            
            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }
    
    @Override
    public Map<String, Object> uploadDatasetFile(Long datasetId, MultipartFile file) {
        Dataset dataset = getDatasetById(datasetId);
        
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("未授权访问");
        }
        
        String username = authentication.getName();
        User creator = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户未找到: " + username));
        
        // 检查权限
        if (!hasAccessToDataset(datasetId, creator.getId()) && !hasAdminRole()) {
            throw new UnauthorizedException("无权上传文件到数据集");
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 确保上传目录存在
            Path uploadPath = Paths.get(uploadDir, "dataset-" + datasetId);
            Files.createDirectories(uploadPath);
            
            // 生成唯一文件名
            String fileName = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();
            Path filePath = uploadPath.resolve(fileName);
            
            // 保存文件
            Files.copy(file.getInputStream(), filePath);
            
            // 创建数据项
            DataItem dataItem = new DataItem();
            dataItem.setName(file.getOriginalFilename());
            dataItem.setType(getDataItemTypeFromFile(file));
            dataItem.setFilePath(filePath.toString());
            dataItem.setFileSize(file.getSize());
            dataItem.setFileType(file.getContentType());
            dataItem.setDataset(dataset);
            dataItem.setCreator(creator);
            dataItem.setCreatedAt(LocalDateTime.now());
            dataItem.setUpdatedAt(LocalDateTime.now());
            
            dataItemRepository.save(dataItem);
            
            // 更新数据集信息
            dataset.setItemCount((int) dataItemRepository.countByDatasetId(datasetId));
            dataset.setUpdatedAt(LocalDateTime.now());
            datasetRepository.save(dataset);
            
            result.put("success", true);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("fileType", file.getContentType());
            result.put("savedPath", filePath.toString());
            
            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }
    
    @Override
    public Page<Dataset> getDatasetsByUser(User user, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findByAuthorizedUsersContaining(user, pageable);
    }
    
    @Override
    public List<Dataset> getDatasetsByProjectId(Long projectId) {
        return datasetRepository.findByProjectId(projectId);
    }
    
    @Override
    public Page<Dataset> getDatasetsByProjectId(Long projectId, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findByProjectId(projectId, pageable);
    }
    
    @Override
    @Transactional
    public Dataset migrateDatasetFromLocalStorage(com.example.biaozhu.dto.DatasetDTO datasetDTO, User creator) {
        // 检查数据集名称是否已存在
        Optional<Dataset> existingDataset = datasetRepository.findByName(datasetDTO.getName());
        if (existingDataset.isPresent()) {
            throw new IllegalArgumentException("数据集名称已存在: " + datasetDTO.getName());
        }
        
        // 创建新的数据集对象
        Dataset dataset = new Dataset();
        dataset.setName(datasetDTO.getName());
        dataset.setDescription(datasetDTO.getDescription());
        dataset.setType(datasetDTO.getType());
        dataset.setCreator(creator);
        
        // 查找并设置项目
        try {
            // 从ProjectRepository获取项目
            Project project = projectRepository.findById(datasetDTO.getProjectId())
                .orElseThrow(() -> new IllegalArgumentException("无法找到ID为 " + datasetDTO.getProjectId() + " 的项目"));
            dataset.setProject(project);
        } catch (Exception e) {
            throw new IllegalArgumentException("设置项目关联失败: " + e.getMessage());
        }
        
        // 设置其他属性
        dataset.setItemCount(datasetDTO.getItemCount());
        dataset.setLabeledCount(datasetDTO.getLabeledCount());
        dataset.setReviewedCount(datasetDTO.getReviewedCount());
        
        // 设置创建和更新时间
        dataset.setCreatedAt(LocalDateTime.now());
        dataset.setUpdatedAt(LocalDateTime.now());
        
        // 转换可见性和状态
        if (datasetDTO.getVisibility() != null) {
            try {
                dataset.setVisibility(Dataset.Visibility.valueOf(datasetDTO.getVisibility()));
            } catch (IllegalArgumentException e) {
                dataset.setVisibility(Dataset.Visibility.PRIVATE);
            }
        }
        
        if (datasetDTO.getStatus() != null) {
            try {
                dataset.setStatus(Dataset.DatasetStatus.valueOf(datasetDTO.getStatus()));
            } catch (IllegalArgumentException e) {
                dataset.setStatus(Dataset.DatasetStatus.ACTIVE);
            }
        }
        
        // 设置标签
        if (datasetDTO.getLabels() != null && !datasetDTO.getLabels().isEmpty()) {
            dataset.setLabels(datasetDTO.getLabels());
        }
        
        // 保存数据集
        return datasetRepository.save(dataset);
    }

    @Override
    public Page<Dataset> getAccessibleDatasetsByTypeAndKeyword(User user, String type, String keyword, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return datasetRepository.findAccessibleDatasetsByTypeAndKeyword(user, type, keyword, pageable);
    }

    @Override
    public long countAccessibleDatasets(User user) {
        return datasetRepository.countAccessibleDatasets(user);
    }

    @Override
    public long countPublicDatasets() {
        return datasetRepository.countPublicDatasets();
    }

    @Override
    public long countDatasetsByCreator(Long userId) {
        return datasetRepository.countByCreatorId(userId);
    }

    @Override
    public long countAuthorizedDatasets(User user) {
        return datasetRepository.countAuthorizedDatasets(user);
    }

    @Override
    public Map<String, Long> getAccessibleDatasetsTypeStats(User user) {
        List<Object[]> results = datasetRepository.getAccessibleDatasetsTypeStats(user);
        Map<String, Long> typeStats = new HashMap<>();

        for (Object[] result : results) {
            String type = (String) result[0];
            Long count = (Long) result[1];
            typeStats.put(type, count);
        }

        return typeStats;
    }
}